class Product {
  final int id;
  final String productName;
  final int categoryId;
  final int? subcategoryId;
  final int? subSubcategoryId;
  final String status;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final double price;
  final String? description;
  final String? image;
  final bool featured;
  final String? previousStatus;
  final int durationMinutes;
  final int numberOfSlots;
  final DateTime? disabledStartAt;
  final DateTime? disabledEndAt;
  final double discountPercentage;
  final String discountType;
  final double flatDiscountAmount;
  final double? afterDiscountPrice;

  Product({
    required this.id,
    required this.productName,
    required this.categoryId,
    this.subcategoryId,
    this.subSubcategoryId,
    required this.status,
    required this.createdAt,
    this.updatedAt,
    required this.price,
    this.description,
    this.image,
    required this.featured,
    this.previousStatus,
    required this.durationMinutes,
    required this.numberOfSlots,
    this.disabledStartAt,
    this.disabledEndAt,
    required this.discountPercentage,
    required this.discountType,
    required this.flatDiscountAmount,
    this.afterDiscountPrice,
  });

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'] as int,
      productName: json['product_name'] as String,
      categoryId: json['category_id'] as int,
      subcategoryId: json['subcategory_id'] as int?,
      subSubcategoryId: json['sub_subcategory_id'] as int?,
      status: json['status'] as String? ?? 'active',
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String) 
          : null,
      price: (json['price'] as num).toDouble(),
      description: json['description'] as String?,
      image: json['image'] as String?,
      featured: json['featured'] as bool? ?? false,
      previousStatus: json['previous_status'] as String?,
      durationMinutes: json['duration_minutes'] as int? ?? 60,
      numberOfSlots: json['number_of_slots'] as int? ?? 0,
      disabledStartAt: json['disabled_start_at'] != null 
          ? DateTime.parse(json['disabled_start_at'] as String) 
          : null,
      disabledEndAt: json['disabled_end_at'] != null 
          ? DateTime.parse(json['disabled_end_at'] as String) 
          : null,
      discountPercentage: (json['discount_percentage'] as num?)?.toDouble() ?? 0.0,
      discountType: json['discount_type'] as String? ?? 'percentage',
      flatDiscountAmount: (json['flat_discount_amount'] as num?)?.toDouble() ?? 0.0,
      afterDiscountPrice: (json['after_discount_price'] as num?)?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'product_name': productName,
      'category_id': categoryId,
      'subcategory_id': subcategoryId,
      'sub_subcategory_id': subSubcategoryId,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'price': price,
      'description': description,
      'image': image,
      'featured': featured,
      'previous_status': previousStatus,
      'duration_minutes': durationMinutes,
      'number_of_slots': numberOfSlots,
      'disabled_start_at': disabledStartAt?.toIso8601String(),
      'disabled_end_at': disabledEndAt?.toIso8601String(),
      'discount_percentage': discountPercentage,
      'discount_type': discountType,
      'flat_discount_amount': flatDiscountAmount,
    };
  }

  bool get isActive => status == 'active';
  bool get hasDiscount => discountPercentage > 0 || flatDiscountAmount > 0;
  bool get isAvailable {
    if (!isActive) return false;
    
    final now = DateTime.now();
    if (disabledStartAt != null && disabledEndAt != null) {
      return !(now.isAfter(disabledStartAt!) && now.isBefore(disabledEndAt!));
    }
    
    return true;
  }

  double get finalPrice => afterDiscountPrice ?? price;
  
  String get formattedDuration {
    if (durationMinutes < 60) {
      return '${durationMinutes} mins';
    } else {
      final hours = durationMinutes ~/ 60;
      final minutes = durationMinutes % 60;
      if (minutes == 0) {
        return '${hours}h';
      } else {
        return '${hours}h ${minutes}m';
      }
    }
  }
}
