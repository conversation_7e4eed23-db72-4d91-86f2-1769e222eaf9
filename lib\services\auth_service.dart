import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_config.dart';

class AuthService {
  static SupabaseClient get _supabase => Supabase.instance.client;

  // Initialize Supabase
  static Future<void> initialize() async {
    await Supabase.initialize(
      url: SupabaseConfig.supabaseUrl,
      anonKey: SupabaseConfig.supabaseAnonKey,
    );
  }

  // Register a new user
  static Future<Map<String, dynamic>> registerUser({
    required String name,
    required String email,
    required String password,
    String? phoneNumber,
  }) async {
    try {
      // Sign up with Supabase Auth
      final AuthResponse authResponse = await _supabase.auth.signUp(
        email: email,
        password: password,
      );

      if (authResponse.user == null) {
        return {'success': false, 'message': 'Failed to create user account'};
      }

      // Insert user data into the users table
      final response = await _supabase.from('users').insert({
        'id': authResponse.user!.id,
        'email': email,
        'full_name': name,
        'name': name,
        'phone_number': phoneNumber,
        'phone': phoneNumber,
        'status': 'active',
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      }).select();

      if (response.isEmpty) {
        return {'success': false, 'message': 'Failed to save user data'};
      }

      return {
        'success': true,
        'message': 'User registered successfully',
        'user': authResponse.user,
        'userData': response.first,
      };
    } catch (e) {
      print('Registration error: $e');
      return {
        'success': false,
        'message': 'Registration failed: ${e.toString()}',
      };
    }
  }

  // Login user
  static Future<Map<String, dynamic>> loginUser({
    required String email,
    required String password,
  }) async {
    try {
      final AuthResponse authResponse = await _supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (authResponse.user == null) {
        return {'success': false, 'message': 'Invalid email or password'};
      }

      // Get user data from users table
      final userData = await _supabase
          .from('users')
          .select()
          .eq('id', authResponse.user!.id)
          .single();

      return {
        'success': true,
        'message': 'Login successful',
        'user': authResponse.user,
        'userData': userData,
      };
    } catch (e) {
      print('Login error: $e');
      return {'success': false, 'message': 'Login failed: ${e.toString()}'};
    }
  }

  // Get current logged in user
  static Future<Map<String, dynamic>?> getCurrentUser() async {
    try {
      final User? user = _supabase.auth.currentUser;

      if (user == null) {
        return null;
      }

      // Get user data from users table
      final userData = await _supabase
          .from('users')
          .select()
          .eq('id', user.id)
          .single();

      return {'user': user, 'userData': userData};
    } catch (e) {
      print('Get current user error: $e');
      return null;
    }
  }

  // Logout user
  static Future<Map<String, dynamic>> logoutUser() async {
    try {
      await _supabase.auth.signOut();
      return {'success': true, 'message': 'Logged out successfully'};
    } catch (e) {
      print('Logout error: $e');
      return {'success': false, 'message': 'Logout failed: ${e.toString()}'};
    }
  }

  // Check if user is logged in
  static Future<bool> isLoggedIn() async {
    final user = _supabase.auth.currentUser;
    return user != null;
  }

  // Get current user session
  static Session? getCurrentSession() {
    return _supabase.auth.currentSession;
  }

  // Get all users (for admin purposes)
  static Future<List<Map<String, dynamic>>> getAllUsers() async {
    try {
      final response = await _supabase
          .from('users')
          .select()
          .order('created_at', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      print('Get all users error: $e');
      return [];
    }
  }
}
