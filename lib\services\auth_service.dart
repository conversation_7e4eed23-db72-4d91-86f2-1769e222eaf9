import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class AuthService {
  static const String _usersKey = 'registered_users';
  static const String _currentUserKey = 'current_user';

  // Register a new user
  static Future<bool> registerUser({
    required String name,
    required String email,
    required String password,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Get existing users
      final usersJson = prefs.getString(_usersKey) ?? '[]';
      final List<dynamic> users = json.decode(usersJson);
      
      // Check if user already exists
      final existingUser = users.firstWhere(
        (user) => user['email'] == email,
        orElse: () => null,
      );
      
      if (existingUser != null) {
        return false; // User already exists
      }
      
      // Add new user
      users.add({
        'name': name,
        'email': email,
        'password': password, // In real app, this should be hashed
        'createdAt': DateTime.now().toIso8601String(),
      });
      
      // Save updated users list
      await prefs.setString(_usersKey, json.encode(users));
      return true;
    } catch (e) {
      print('Registration error: $e');
      return false;
    }
  }

  // Login user
  static Future<Map<String, dynamic>?> loginUser({
    required String email,
    required String password,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Get existing users
      final usersJson = prefs.getString(_usersKey) ?? '[]';
      final List<dynamic> users = json.decode(usersJson);
      
      // Find user with matching credentials
      final user = users.firstWhere(
        (user) => user['email'] == email && user['password'] == password,
        orElse: () => null,
      );
      
      if (user != null) {
        // Save current user
        await prefs.setString(_currentUserKey, json.encode(user));
        return Map<String, dynamic>.from(user);
      }
      
      return null; // Invalid credentials
    } catch (e) {
      print('Login error: $e');
      return null;
    }
  }

  // Get current logged in user
  static Future<Map<String, dynamic>?> getCurrentUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(_currentUserKey);
      
      if (userJson != null) {
        return Map<String, dynamic>.from(json.decode(userJson));
      }
      
      return null;
    } catch (e) {
      print('Get current user error: $e');
      return null;
    }
  }

  // Logout user
  static Future<void> logoutUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_currentUserKey);
    } catch (e) {
      print('Logout error: $e');
    }
  }

  // Check if user is logged in
  static Future<bool> isLoggedIn() async {
    final user = await getCurrentUser();
    return user != null;
  }

  // Get all registered users (for debugging)
  static Future<List<Map<String, dynamic>>> getAllUsers() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final usersJson = prefs.getString(_usersKey) ?? '[]';
      final List<dynamic> users = json.decode(usersJson);
      
      return users.map((user) => Map<String, dynamic>.from(user)).toList();
    } catch (e) {
      print('Get all users error: $e');
      return [];
    }
  }
}
