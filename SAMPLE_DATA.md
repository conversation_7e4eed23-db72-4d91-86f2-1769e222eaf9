# DODOBOOKER - Sample Data Setup

## 🗃️ Sample Categories Data

To test your app, you can insert this sample data into your Supabase `categories` table:

```sql
INSERT INTO categories (name, image, featured, status, priority, start_time, end_time) VALUES
('Carpentry', null, true, 'active', 1, '09:00:00', '17:00:00'),
('Plumbing', null, true, 'active', 2, '08:00:00', '18:00:00'),
('Electricals', null, false, 'active', 3, '09:00:00', '17:00:00'),
('Appliances', null, false, 'active', 4, '10:00:00', '16:00:00'),
('Pestcare', null, true, 'active', 5, '09:00:00', '17:00:00'),
('Cleaning', null, true, 'active', 6, '08:00:00', '20:00:00'),
('Gardening', null, false, 'active', 7, '07:00:00', '17:00:00'),
('Glass Shine', null, false, 'active', 8, '09:00:00', '17:00:00');
```

## 🗃️ Sample Subcategories Data

```sql
INSERT INTO subcategories (name, image, featured, status, category_id) VALUES
-- Carpentry subcategories
('Furniture Repair', null, true, 'active', 1),
('Door Installation', null, false, 'active', 1),
('Cabinet Making', null, false, 'active', 1),

-- Plumbing subcategories  
('Pipe Repair', null, true, 'active', 2),
('Bathroom Fitting', null, true, 'active', 2),
('Kitchen Plumbing', null, false, 'active', 2),

-- Cleaning subcategories
('House Cleaning', null, true, 'active', 6),
('Deep Cleaning', null, true, 'active', 6),
('Office Cleaning', null, false, 'active', 6);
```

## 🗃️ Sample Products Data

```sql
INSERT INTO products (product_name, category_id, subcategory_id, status, price, description, featured, duration_minutes, discount_percentage) VALUES
-- Carpentry products
('Furniture Assembly', 1, 1, 'active', 500.00, 'Professional furniture assembly service for all types of furniture', true, 120, 10.0),
('Door Repair', 1, 2, 'active', 800.00, 'Complete door repair and maintenance service', false, 90, 0.0),
('Custom Cabinet', 1, 3, 'active', 2500.00, 'Custom cabinet design and installation', true, 240, 15.0),

-- Plumbing products
('Pipe Leak Repair', 2, 1, 'active', 300.00, 'Quick and reliable pipe leak repair service', true, 60, 20.0),
('Bathroom Installation', 2, 2, 'active', 1500.00, 'Complete bathroom fitting and installation', true, 180, 5.0),
('Kitchen Sink Repair', 2, 3, 'active', 400.00, 'Kitchen sink and faucet repair service', false, 75, 0.0),

-- Cleaning products
('Regular House Cleaning', 6, 1, 'active', 600.00, 'Regular house cleaning service for 2-3 BHK', true, 120, 0.0),
('Deep Cleaning Service', 6, 2, 'active', 1200.00, 'Comprehensive deep cleaning for entire house', true, 240, 25.0),
('Office Cleaning', 6, 3, 'active', 800.00, 'Professional office cleaning service', false, 150, 10.0),

-- Direct category products (no subcategory)
('AC Repair', 4, null, 'active', 700.00, 'Air conditioner repair and maintenance', true, 90, 15.0),
('Pest Control', 5, null, 'active', 900.00, 'Complete pest control treatment for home', true, 120, 20.0),
('Garden Maintenance', 7, null, 'active', 500.00, 'Regular garden maintenance and care', false, 180, 0.0);
```

## 🚀 How to Insert Data

1. **Open your Supabase Dashboard**
2. **Go to SQL Editor**
3. **Copy and paste each SQL block above**
4. **Run each query**

## 📱 Testing the App

After inserting the sample data:

1. **Update your Supabase credentials** in `lib/config/supabase_config.dart`
2. **Run the app**: `flutter run`
3. **Create a new account** or **login**
4. **You should see the categories** on the home page
5. **Tap on any category** to see subcategories and products
6. **Tap on products** to see detailed product information

## 🎨 Features You'll See

- ✅ **Beautiful home page** with greeting and search
- ✅ **Category grid** with custom icons and colors
- ✅ **Category detail page** with tabs for subcategories and products
- ✅ **Product detail page** with pricing and add to cart
- ✅ **Proper navigation** between screens
- ✅ **Search functionality** for categories
- ✅ **Responsive design** that looks great

## 🔧 Customization

You can customize:
- **Category icons** in `_getCategoryIcon()` method
- **Category colors** in `_getCategoryColors()` method  
- **Product images** by adding image URLs to your database
- **Pricing display** and discount calculations
- **Service duration** formatting

The app is now ready for your home services business! 🏠✨
