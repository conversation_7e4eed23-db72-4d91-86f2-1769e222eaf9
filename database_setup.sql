-- DODOBOOKER Database Setup
-- Run these queries in your Supabase SQL Editor

-- 1. First, create the users table
CREATE TABLE public.users (
  id text PRIMARY KEY,
  email text UNIQUE,
  full_name text,
  avatar_url text,
  status text DEFAULT 'active',
  last_login timestamp with time zone,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
  phone_number text,
  name text,
  phone text
);

-- 2. Create categories table
CREATE TABLE public.categories (
  id bigint GENERATED BY DEFAULT AS IDENTITY NOT NULL,
  name text NOT NULL,
  image text,
  featured boolean DEFAULT false,
  status text DEFAULT 'active'::text,
  created_at timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now()),
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
  priority integer DEFAULT 0,
  start_time time without time zone DEFAULT '09:00:00'::time without time zone,
  end_time time without time zone DEFAULT '17:00:00'::time without time zone,
  CONSTRAINT categories_pkey PRIMARY KEY (id),
  CONSTRAINT categories_status_check CHECK (status = ANY (ARRAY['active'::text, 'inactive'::text]))
);

-- 3. Create subcategories table
CREATE TABLE public.subcategories (
  id bigint GENERATED BY DEFAULT AS IDENTITY NOT NULL,
  name text NOT NULL,
  image text,
  featured boolean DEFAULT false,
  status text DEFAULT 'active'::text,
  category_id bigint NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now()),
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
  previous_status character varying(20) DEFAULT null::character varying,
  CONSTRAINT subcategories_pkey PRIMARY KEY (id),
  CONSTRAINT subcategories_category_id_fkey FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE CASCADE,
  CONSTRAINT subcategories_status_check CHECK (status = ANY (ARRAY['active'::text, 'inactive'::text]))
);

-- 4. Create subsubcategories table
CREATE TABLE public.subsubcategories (
  id bigserial NOT NULL,
  name character varying(255) NOT NULL,
  image character varying(255),
  featured boolean DEFAULT false,
  status character varying(50) DEFAULT 'active'::character varying,
  category_id bigint NOT NULL,
  subcategory_id bigint NOT NULL,
  created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
  previous_status character varying(20) DEFAULT null::character varying,
  CONSTRAINT subsubcategories_pkey PRIMARY KEY (id),
  CONSTRAINT unique_subsubcategory_name_per_subcategory UNIQUE (name, subcategory_id),
  CONSTRAINT fk_category FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE CASCADE,
  CONSTRAINT fk_subcategory FOREIGN KEY (subcategory_id) REFERENCES subcategories (id) ON DELETE CASCADE,
  CONSTRAINT check_subsubcategory_status CHECK (
    (status)::text = ANY (
      (ARRAY[
        'active'::character varying,
        'inactive'::character varying,
        'pending'::character varying,
        'deleted'::character varying
      ])::text[]
    )
  )
);

-- 5. Create products table
CREATE TABLE public.products (
  id bigint GENERATED BY DEFAULT AS IDENTITY NOT NULL,
  product_name text NOT NULL,
  category_id bigint NOT NULL,
  subcategory_id bigint,
  sub_subcategory_id bigint,
  status text DEFAULT 'active'::text,
  created_at timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now()),
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
  price numeric(10, 2) NOT NULL,
  description text,
  image text,
  featured boolean DEFAULT false,
  previous_status character varying(20),
  duration_minutes integer DEFAULT 60,
  number_of_slots integer DEFAULT 0,
  disabled_start_at timestamp with time zone,
  disabled_end_at timestamp with time zone,
  discount_percentage numeric(5, 2) DEFAULT 0,
  discount_type text DEFAULT 'percentage'::text,
  flat_discount_amount numeric(10, 2) DEFAULT 0,
  after_discount_price numeric GENERATED ALWAYS AS (
    CASE
      WHEN (discount_type = 'percentage'::text) THEN round(
        (price - ((price * discount_percentage) / (100)::numeric)), 2
      )
      WHEN (discount_type = 'flat'::text) THEN round((price - flat_discount_amount), 2)
      ELSE price
    END
  ) STORED,
  CONSTRAINT products_pkey PRIMARY KEY (id),
  CONSTRAINT fk_category FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE CASCADE,
  CONSTRAINT fk_sub_subcategory FOREIGN KEY (sub_subcategory_id) REFERENCES subsubcategories (id) ON DELETE CASCADE,
  CONSTRAINT fk_subcategory FOREIGN KEY (subcategory_id) REFERENCES subcategories (id) ON DELETE CASCADE,
  CONSTRAINT products_price_check CHECK ((price >= (0)::numeric)),
  CONSTRAINT products_status_check CHECK (status = ANY (ARRAY['active'::text, 'inactive'::text]))
);

-- 6. Create coupons table (required for coupon_subcategory and coupon_subsubcategory)
CREATE TABLE public.coupons (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  code text NOT NULL UNIQUE,
  name text NOT NULL,
  description text,
  discount_type text DEFAULT 'percentage'::text,
  discount_value numeric(10, 2) NOT NULL,
  min_order_amount numeric(10, 2) DEFAULT 0,
  max_discount_amount numeric(10, 2),
  usage_limit integer,
  used_count integer DEFAULT 0,
  status text DEFAULT 'active'::text,
  valid_from timestamp with time zone DEFAULT timezone('utc'::text, now()),
  valid_until timestamp with time zone,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
  CONSTRAINT coupons_pkey PRIMARY KEY (id),
  CONSTRAINT coupons_status_check CHECK (status = ANY (ARRAY['active'::text, 'inactive'::text, 'expired'::text]))
);

-- 7. Create coupon_subcategory table
CREATE TABLE public.coupon_subcategory (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  coupon_id uuid NOT NULL,
  subcategory_id bigint NOT NULL,
  CONSTRAINT coupon_subcategory_pkey PRIMARY KEY (id),
  CONSTRAINT coupon_subcategory_coupon_id_fkey FOREIGN KEY (coupon_id) REFERENCES coupons (id) ON DELETE CASCADE,
  CONSTRAINT coupon_subcategory_subcategory_id_fkey FOREIGN KEY (subcategory_id) REFERENCES subcategories (id) ON DELETE CASCADE
);

-- 8. Create coupon_subsubcategory table
CREATE TABLE public.coupon_subsubcategory (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  coupon_id uuid NOT NULL,
  subsubcategory_id bigint NOT NULL,
  CONSTRAINT coupon_subsubcategory_pkey PRIMARY KEY (id),
  CONSTRAINT coupon_subsubcategory_coupon_id_fkey FOREIGN KEY (coupon_id) REFERENCES coupons (id) ON DELETE CASCADE,
  CONSTRAINT coupon_subsubcategory_subsubcategory_id_fkey FOREIGN KEY (subsubcategory_id) REFERENCES subsubcategories (id) ON DELETE CASCADE
);

-- 9. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_categories_priority ON public.categories USING btree (priority);
CREATE INDEX IF NOT EXISTS idx_subsubcategories_category_id ON public.subsubcategories USING btree (category_id);
CREATE INDEX IF NOT EXISTS idx_subsubcategories_subcategory_id ON public.subsubcategories USING btree (subcategory_id);
CREATE INDEX IF NOT EXISTS idx_subsubcategories_status ON public.subsubcategories USING btree (status);
CREATE INDEX IF NOT EXISTS idx_subsubcategories_featured ON public.subsubcategories USING btree (featured);
CREATE INDEX IF NOT EXISTS idx_products_category_id ON public.products USING btree (category_id);
CREATE INDEX IF NOT EXISTS idx_products_subcategory_id ON public.products USING btree (subcategory_id);
CREATE INDEX IF NOT EXISTS idx_products_sub_subcategory_id ON public.products USING btree (sub_subcategory_id);
CREATE INDEX IF NOT EXISTS idx_products_status ON public.products USING btree (status);
CREATE INDEX IF NOT EXISTS idx_products_featured ON public.products USING btree (featured);
CREATE INDEX IF NOT EXISTS idx_products_created_at ON public.products USING btree (created_at);
CREATE INDEX IF NOT EXISTS idx_products_updated_at ON public.products USING btree (updated_at);

-- 10. Enable Row Level Security (RLS) for users table
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- 11. Create RLS policies for users table
CREATE POLICY "Users can view own profile" ON public.users
  FOR SELECT USING (auth.uid()::text = id);

CREATE POLICY "Users can update own profile" ON public.users
  FOR UPDATE USING (auth.uid()::text = id);

-- 12. Create a function to handle user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.users (id, email, full_name, name)
  VALUES (new.id, new.email, new.raw_user_meta_data->>'full_name', new.raw_user_meta_data->>'name');
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 13. Create trigger to automatically create user profile
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();
