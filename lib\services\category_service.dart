import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/category.dart';
import '../models/subcategory.dart';
import '../models/subsubcategory.dart';
import '../models/product.dart';

class CategoryService {
  static SupabaseClient get _supabase => Supabase.instance.client;

  // Get all active categories ordered by priority
  static Future<List<Category>> getCategories() async {
    try {
      print('🔄 Fetching categories from database...');
      final response = await _supabase
          .from('category')
          .select()
          .eq('status', 'active')
          .order('priority', ascending: true)
          .order('name', ascending: true);

      print('📊 Raw response: $response');
      print('📊 Response type: ${response.runtimeType}');
      print('📊 Response length: ${response.length}');

      if (response.isEmpty) {
        print('⚠️ No categories found in database');
        return [];
      }

      final categories = response.map((json) {
        print('🔄 Processing category: $json');
        return Category.fromJson(json);
      }).toList();

      print('✅ Successfully loaded ${categories.length} categories');
      return categories;
    } catch (e, stackTrace) {
      print('❌ Error fetching categories: $e');
      print('📍 Stack trace: $stackTrace');
      print('🔄 Returning test categories due to error');
      return _getTestCategories();
    }
  }

  // Test categories for when database is empty
  static List<Category> _getTestCategories() {
    return [
      Category(
        id: 1,
        name: 'Cleaning',
        image:
            'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop',
        featured: true,
        status: 'active',
        priority: 1,
        startTime: '08:00:00',
        endTime: '20:00:00',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Category(
        id: 2,
        name: 'Plumbing',
        image:
            'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop',
        featured: true,
        status: 'active',
        priority: 2,
        startTime: '09:00:00',
        endTime: '18:00:00',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Category(
        id: 3,
        name: 'Repair',
        image:
            'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=300&h=300&fit=crop',
        featured: true,
        status: 'active',
        priority: 3,
        startTime: '09:00:00',
        endTime: '17:00:00',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Category(
        id: 4,
        name: 'Painting',
        image:
            'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=300&h=300&fit=crop',
        featured: false,
        status: 'active',
        priority: 4,
        startTime: '08:00:00',
        endTime: '18:00:00',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Category(
        id: 5,
        name: 'Washing',
        image:
            'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop',
        featured: false,
        status: 'active',
        priority: 5,
        startTime: '08:00:00',
        endTime: '17:00:00',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Category(
        id: 6,
        name: 'All Services',
        image:
            'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop',
        featured: false,
        status: 'active',
        priority: 6,
        startTime: '10:00:00',
        endTime: '19:00:00',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];
  }

  // Get featured categories
  static Future<List<Category>> getFeaturedCategories() async {
    try {
      final response = await _supabase
          .from('category')
          .select()
          .eq('status', 'active')
          .eq('featured', true)
          .order('priority', ascending: true)
          .order('name', ascending: true);

      return response.map((json) => Category.fromJson(json)).toList();
    } catch (e) {
      print('Error fetching featured categories: $e');
      return [];
    }
  }

  // Get subcategories for a specific category
  static Future<List<Subcategory>> getSubcategories(int categoryId) async {
    try {
      final response = await _supabase
          .from('Subcategory')
          .select()
          .eq('category_id', categoryId)
          .eq('status', 'active')
          .order('name', ascending: true);

      return response.map((json) => Subcategory.fromJson(json)).toList();
    } catch (e) {
      print('Error fetching subcategories: $e');
      return [];
    }
  }

  // Get subsubcategories for a specific subcategory
  static Future<List<SubSubCategory>> getSubsubcategories(
    int subcategoryId,
  ) async {
    try {
      final response = await _supabase
          .from('SubSubCategory')
          .select()
          .eq('subcategory_id', subcategoryId)
          .eq('status', 'active')
          .order('name', ascending: true);

      return response.map((json) => SubSubCategory.fromJson(json)).toList();
    } catch (e) {
      print('Error fetching subsubcategories: $e');
      return [];
    }
  }

  // Get products for a category
  static Future<List<Product>> getProductsByCategory(int categoryId) async {
    try {
      final response = await _supabase
          .from('Products')
          .select()
          .eq('category_id', categoryId)
          .eq('status', 'active')
          .order('featured', ascending: false)
          .order('product_name', ascending: true);

      return response.map((json) => Product.fromJson(json)).toList();
    } catch (e) {
      print('Error fetching products by category: $e');
      return [];
    }
  }

  // Get products for a subcategory
  static Future<List<Product>> getProductsBySubcategory(
    String subcategoryId,
  ) async {
    try {
      final response = await _supabase
          .from('Products')
          .select()
          .eq('subcategory_id', subcategoryId)
          .eq('status', 'active')
          .order('featured', ascending: false)
          .order('product_name', ascending: true);

      return response.map((json) => Product.fromJson(json)).toList();
    } catch (e) {
      print('Error fetching products by subcategory: $e');
      return [];
    }
  }

  // Get products for a subsubcategory
  static Future<List<Product>> getProductsBySubsubcategory(
    String subsubcategoryId,
  ) async {
    try {
      final response = await _supabase
          .from('Products')
          .select()
          .eq('sub_subcategory_id', subsubcategoryId)
          .eq('status', 'active')
          .order('featured', ascending: false)
          .order('product_name', ascending: true);

      return response.map((json) => Product.fromJson(json)).toList();
    } catch (e) {
      print('Error fetching products by subsubcategory: $e');
      return [];
    }
  }

  // Search products by name
  static Future<List<Product>> searchProducts(String query) async {
    try {
      final response = await _supabase
          .from('Products')
          .select()
          .eq('status', 'active')
          .ilike('product_name', '%$query%')
          .order('featured', ascending: false)
          .order('product_name', ascending: true);

      return response.map((json) => Product.fromJson(json)).toList();
    } catch (e) {
      print('Error searching products: $e');
      return [];
    }
  }

  // Get featured products
  static Future<List<Product>> getFeaturedProducts() async {
    try {
      final response = await _supabase
          .from('Products')
          .select()
          .eq('status', 'active')
          .eq('featured', true)
          .order('product_name', ascending: true);

      return response.map((json) => Product.fromJson(json)).toList();
    } catch (e) {
      print('Error fetching featured products: $e');
      return [];
    }
  }

  // Get a single category by ID
  static Future<Category?> getCategoryById(int categoryId) async {
    try {
      final response = await _supabase
          .from('category')
          .select()
          .eq('id', categoryId)
          .eq('status', 'active')
          .single();

      return Category.fromJson(response);
    } catch (e) {
      print('Error fetching category by ID: $e');
      return null;
    }
  }

  // Get a single product by ID
  static Future<Product?> getProductById(int productId) async {
    try {
      final response = await _supabase
          .from('Products')
          .select()
          .eq('id', productId)
          .eq('status', 'active')
          .single();

      return Product.fromJson(response);
    } catch (e) {
      print('Error fetching product by ID: $e');
      return null;
    }
  }
}
