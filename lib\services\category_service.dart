import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/category.dart';
import '../models/subcategory.dart';
import '../models/subsubcategory.dart';
import '../models/product.dart';

class CategoryService {
  static SupabaseClient get _supabase => Supabase.instance.client;

  // Get all active categories ordered by priority
  static Future<List<Category>> getCategories() async {
    try {
      final response = await _supabase
          .from('categories')
          .select()
          .eq('status', 'active')
          .order('priority', ascending: true)
          .order('name', ascending: true);

      return response.map((json) => Category.fromJson(json)).toList();
    } catch (e) {
      print('Error fetching categories: $e');
      return [];
    }
  }

  // Get featured categories
  static Future<List<Category>> getFeaturedCategories() async {
    try {
      final response = await _supabase
          .from('categories')
          .select()
          .eq('status', 'active')
          .eq('featured', true)
          .order('priority', ascending: true)
          .order('name', ascending: true);

      return response.map((json) => Category.fromJson(json)).toList();
    } catch (e) {
      print('Error fetching featured categories: $e');
      return [];
    }
  }

  // Get subcategories for a specific category
  static Future<List<Subcategory>> getSubcategories(int categoryId) async {
    try {
      final response = await _supabase
          .from('subcategories')
          .select()
          .eq('category_id', categoryId)
          .eq('status', 'active')
          .order('name', ascending: true);

      return response.map((json) => Subcategory.fromJson(json)).toList();
    } catch (e) {
      print('Error fetching subcategories: $e');
      return [];
    }
  }

  // Get subsubcategories for a specific subcategory
  static Future<List<Subsubcategory>> getSubsubcategories(int subcategoryId) async {
    try {
      final response = await _supabase
          .from('subsubcategories')
          .select()
          .eq('subcategory_id', subcategoryId)
          .eq('status', 'active')
          .order('name', ascending: true);

      return response.map((json) => Subsubcategory.fromJson(json)).toList();
    } catch (e) {
      print('Error fetching subsubcategories: $e');
      return [];
    }
  }

  // Get products for a category
  static Future<List<Product>> getProductsByCategory(int categoryId) async {
    try {
      final response = await _supabase
          .from('products')
          .select()
          .eq('category_id', categoryId)
          .eq('status', 'active')
          .order('featured', ascending: false)
          .order('product_name', ascending: true);

      return response.map((json) => Product.fromJson(json)).toList();
    } catch (e) {
      print('Error fetching products by category: $e');
      return [];
    }
  }

  // Get products for a subcategory
  static Future<List<Product>> getProductsBySubcategory(int subcategoryId) async {
    try {
      final response = await _supabase
          .from('products')
          .select()
          .eq('subcategory_id', subcategoryId)
          .eq('status', 'active')
          .order('featured', ascending: false)
          .order('product_name', ascending: true);

      return response.map((json) => Product.fromJson(json)).toList();
    } catch (e) {
      print('Error fetching products by subcategory: $e');
      return [];
    }
  }

  // Get products for a subsubcategory
  static Future<List<Product>> getProductsBySubsubcategory(int subsubcategoryId) async {
    try {
      final response = await _supabase
          .from('products')
          .select()
          .eq('sub_subcategory_id', subsubcategoryId)
          .eq('status', 'active')
          .order('featured', ascending: false)
          .order('product_name', ascending: true);

      return response.map((json) => Product.fromJson(json)).toList();
    } catch (e) {
      print('Error fetching products by subsubcategory: $e');
      return [];
    }
  }

  // Search products by name
  static Future<List<Product>> searchProducts(String query) async {
    try {
      final response = await _supabase
          .from('products')
          .select()
          .eq('status', 'active')
          .ilike('product_name', '%$query%')
          .order('featured', ascending: false)
          .order('product_name', ascending: true);

      return response.map((json) => Product.fromJson(json)).toList();
    } catch (e) {
      print('Error searching products: $e');
      return [];
    }
  }

  // Get featured products
  static Future<List<Product>> getFeaturedProducts() async {
    try {
      final response = await _supabase
          .from('products')
          .select()
          .eq('status', 'active')
          .eq('featured', true)
          .order('product_name', ascending: true);

      return response.map((json) => Product.fromJson(json)).toList();
    } catch (e) {
      print('Error fetching featured products: $e');
      return [];
    }
  }

  // Get a single category by ID
  static Future<Category?> getCategoryById(int categoryId) async {
    try {
      final response = await _supabase
          .from('categories')
          .select()
          .eq('id', categoryId)
          .eq('status', 'active')
          .single();

      return Category.fromJson(response);
    } catch (e) {
      print('Error fetching category by ID: $e');
      return null;
    }
  }

  // Get a single product by ID
  static Future<Product?> getProductById(int productId) async {
    try {
      final response = await _supabase
          .from('products')
          .select()
          .eq('id', productId)
          .eq('status', 'active')
          .single();

      return Product.fromJson(response);
    } catch (e) {
      print('Error fetching product by ID: $e');
      return null;
    }
  }
}
