class Subsubcategory {
  final int id;
  final String name;
  final String? image;
  final bool featured;
  final String status;
  final int categoryId;
  final int subcategoryId;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? previousStatus;

  Subsubcategory({
    required this.id,
    required this.name,
    this.image,
    required this.featured,
    required this.status,
    required this.categoryId,
    required this.subcategoryId,
    this.createdAt,
    this.updatedAt,
    this.previousStatus,
  });

  factory Subsubcategory.fromJson(Map<String, dynamic> json) {
    return Subsubcategory(
      id: json['id'] as int,
      name: json['name'] as String,
      image: json['image'] as String?,
      featured: json['featured'] as bool? ?? false,
      status: json['status'] as String? ?? 'active',
      categoryId: json['category_id'] as int,
      subcategoryId: json['subcategory_id'] as int,
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String) 
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String) 
          : null,
      previousStatus: json['previous_status'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'image': image,
      'featured': featured,
      'status': status,
      'category_id': categoryId,
      'subcategory_id': subcategoryId,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'previous_status': previousStatus,
    };
  }

  bool get isActive => status == 'active';
}
