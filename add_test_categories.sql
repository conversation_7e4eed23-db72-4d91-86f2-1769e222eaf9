-- Quick test categories for DODOBOOKER
-- Run this in your Supabase SQL editor to add test data

INSERT INTO category (id, name, image, featured, status, created_at, updated_at, priority, start_time, end_time) VALUES
(1, 'Cleaning', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', true, 'active', NOW(), NOW(), 1, '08:00:00', '20:00:00'),
(2, 'Plumbing', 'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=300&fit=crop', true, 'active', NOW(), NOW(), 2, '09:00:00', '18:00:00'),
(3, 'Repair', 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=300&h=300&fit=crop', true, 'active', NOW(), NOW(), 3, '09:00:00', '17:00:00'),
(4, 'Painting', 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=300&h=300&fit=crop', false, 'active', NOW(), NOW(), 4, '08:00:00', '18:00:00'),
(5, 'Washing', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', false, 'active', NOW(), NOW(), 5, '08:00:00', '17:00:00'),
(6, 'All Services', 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop', false, 'active', NOW(), NOW(), 6, '10:00:00', '19:00:00')
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  image = EXCLUDED.image,
  featured = EXCLUDED.featured,
  status = EXCLUDED.status,
  updated_at = NOW();
